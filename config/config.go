package config

import (
	"log/slog"

	"github.com/spf13/pflag"
	"github.com/spf13/viper"
	_ "github.com/spf13/viper/remote"
)

type Config struct {
	MySqlUrl     string
	RedisUrl     string
	AuthStrategy string
	Secret       string
}

var NowConfig *Config

func initViper() {
	// path 是 etcd 的 key 名字
	err := viper.AddRemoteProvider("etcd3", "http://localhost:12379", "xls")
	if err != nil {
		slog.Error("连接不上etcd", "err", err)
	}

	viper.SetConfigType("toml")

	// 监听 etcd 的 key 变化, 并更新配置
	err = viper.WatchRemoteConfig()
	if err != nil {
		slog.Error("监听不上etcd", "err", err)
	}

	err = viper.ReadRemoteConfig()
	if err != nil {
		slog.Error("读取etcd配置失败", "err", err)
	}

	cfile := pflag.String("config", "./config/dev.toml", "config file")
	pflag.Parse()
	viper.SetConfigFile(*cfile)
	err = viper.ReadInConfig()
	if err != nil {
		slog.Error("读取本地配置失败", "err", err)
	}
}

func InitConfig() {
	initViper()
	NowConfig = &Config{
		MySqlUrl:     viper.GetString("mysql.url"),
		RedisUrl:     viper.GetString("redis.url"),
		AuthStrategy: viper.GetString("auth.strategy"),
		Secret:       viper.GetString("auth.secret"),
	}
	slog.Info("当前配置", "NowConfig", NowConfig)
}
