package tokentool

import (
	"context"
	"time"
)

type Token interface {
	GenerateAccessToken(data any) (string, error)
	GenerateRefreshToken(data any) (string, error)
	RefreshToken(ctx context.Context, tokenStr string) (string, error)
	InvalidToken(ctx context.Context, tokenStr string) error
	CountToken(ctx context.Context, tokenStr string) (int64, error)
	ParseToken(tokenStr string) (Claims, error)
}

type Claims interface {
	GetStdExpiresAt() time.Time
	GetStdSubject() string
	GetData() any
}
