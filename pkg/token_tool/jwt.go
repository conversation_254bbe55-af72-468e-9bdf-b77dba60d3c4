package tokentool

import (
	"context"
	"errors"
	"fmt"
	"time"
	"xls/config"
	"xls/internal/model"

	"github.com/golang-jwt/jwt/v5"
	"github.com/redis/go-redis/v9"
)

var ErrIncorrectRefreshToken = errors.New("token不是refresh token")

var ErrTokenExpired = errors.New("token过期")

type AuthUserClaims struct {
	jwt.RegisteredClaims
	model.AuthUser
}

func (me *AuthUserClaims) GetStdExpiresAt() time.Time {
	return me.ExpiresAt.Time
}

func (me *AuthUserClaims) GetStdSubject() string {
	return me.Subject
}

func (me *AuthUserClaims) GetData() any {
	return me.AuthUser
}

var AuthUserKey = "auth_user"

type JWT struct {
	secret string
	redis  redis.Cmdable
}

var _ Token = (*JWT)(nil)

func NewJWTToken(redis redis.Cmdable) *JWT {
	return &JWT{secret: config.NowConfig.Secret, redis: redis}
}

func (me *JWT) GenerateAccessToken(data any) (string, error) {
	claims := AuthUserClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   "access_token",
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(5 * time.Minute)),
		},
		AuthUser: data.(model.AuthUser),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenStr, err := token.SignedString([]byte(me.secret))
	if err != nil {
		return "", err
	}
	return tokenStr, nil
}

func (me *JWT) GenerateRefreshToken(data any) (string, error) {
	claims := AuthUserClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   "refresh_token",
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(5 * time.Minute)),
		},
		AuthUser: data.(model.AuthUser),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenStr, err := token.SignedString([]byte(me.secret))
	if err != nil {
		return "", err
	}
	return tokenStr, nil
}

func (me *JWT) RefreshToken(ctx context.Context, tokenStr string) (string, error) {
	claims, err := me.ParseToken(tokenStr)
	if err != nil {
		return "", err
	}

	if claims.GetStdSubject() != "refresh_token" {
		return "", ErrIncorrectRefreshToken
	}

	count, err := me.redis.Exists(ctx, me.RedisKey(tokenStr)).Result()
	if err != nil {
		return "", err
	}
	if count > 0 {
		return "", ErrTokenExpired
	}

	accessTokenStr, err := me.GenerateAccessToken(claims.GetData())
	if err != nil {
		return "", err
	}

	return accessTokenStr, nil
}

func (me *JWT) InvalidToken(ctx context.Context, tokenStr string) error {
	return me.redis.Set(ctx, me.RedisKey(tokenStr), "", 30*time.Minute).Err()
}

func (me *JWT) CountToken(ctx context.Context, tokenStr string) (int64, error) {
	return me.redis.Exists(ctx, me.RedisKey(tokenStr)).Result()
}

func (me *JWT) ParseToken(tokenStr string) (Claims, error) {
	claims := &AuthUserClaims{}
	token, err := jwt.ParseWithClaims(tokenStr, claims,
		func(token *jwt.Token) (any, error) {
			return []byte(me.secret), nil
		})
	if err != nil || !token.Valid {
		return claims, err
	}
	return claims, nil
}

func (me *JWT) RedisKey(tokenStr string) string {
	return fmt.Sprintf("%s:%s", AuthUserKey, tokenStr)
}
