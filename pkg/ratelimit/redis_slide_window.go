package ratelimit

import (
	"context"
	_ "embed"
	"time"

	"github.com/redis/go-redis/v9"
)

//go:embed slide_window.lua
var slideWindowLua string

type RedisSlideWindowLimiter struct {
	client redis.Cmdable
	// 一个interval的时间内允许rate个请求
	interval time.Duration
	rate     int
}

func NewRedisSlideWindowLimiter(client redis.Cmdable, interval time.Duration, rate int) *RedisSlideWindowLimiter {
	return &RedisSlideWindowLimiter{
		client:   client,
		interval: interval,
		rate:     rate,
	}
}

func (me *RedisSlideWindowLimiter) Limit(ctx context.Context, key string) (bool, error) {
	now := time.Now().UnixMilli()
	return me.client.Eval(ctx, slideWindowLua,
		[]string{key}, me.interval.Milliseconds(), me.rate, now).Bool()
}
