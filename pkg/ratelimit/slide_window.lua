local key = KEYS[1]
-- 窗口大小, 以时间为单位
local window = tonumber(ARGV[1])
-- 阈值
local rate = tonumber(ARGV[2])
-- 当前时间
local now = tonumber(ARGV[3])

local start_time = now - window

-- 删除过期的请求
redis.call("zremrangebyscore", key, 0, start_time)
-- 获取还存在的请求数
local count = redis.call("zcard", key)

if count >= rate then
    return "true"
else
    -- 添加当前请求, key score value
    redis.call("zadd", key, now, now)
    -- 设置过期时间
    redis.call("pexpire", key, window)
    return "false"
end
