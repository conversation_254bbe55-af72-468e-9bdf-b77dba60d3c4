name: xls
services:
  mysql:
    image: mysql:latest
    restart: always
    command: [ '--default-authentication-plugin=caching_sha2_password' ]
    environment:
      MYSQL_ROOT_PASSWORD: root
    # 设置初始化数据库的脚本的位置
    # MySQL Docker 镜像在初始化数据库时，
    # 会扫描 /docker-entrypoint-initdb.d/ 目录中的 SQL 脚本和其他文件
    volumes:
      - ../../script/mysql/:/docker-entrypoint-initdb.d/
    # 将主机的端口映射到容器的端口, 访问主机的端口就相当于访问容器的端口
    ports:
      - "3306:3306"
  redis:
    image: redis:latest
    restart: always
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"
  etcd:
    image: bitnami/etcd:latest
    restart: always
    ports:
      - "12379:2379"
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
