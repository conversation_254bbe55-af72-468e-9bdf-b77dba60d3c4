apiVersion: apps/v1
kind: Deployment
metadata:
  name: xls-mysql
spec:
  # pod的副本数
  replicas: 1
  selector:
    matchLabels:
      app: xls-mysql
  # 描述pod是怎么样的
  template:
    metadata:
      # pod的标签要和selector的标签一致
      labels:
        app: xls-mysql
    # pod的具体规范
    spec:
      containers:
        - name: xls-mysql
          image: mysql:8.0
          # 优先使用本地,没有再从仓库拉取
          imagePullPolicy: IfNotPresent
          # 使用哪个存储卷
          volumeMounts:
            - name: mysql-storage
              # 哪个目录挂载到存储卷上
              # mysql的镜像规定数据存储在这个目录下
              mountPath: /var/lib/mysql
              # 初始化脚本
            - name: mysql-init-script
              mountPath: /docker-entrypoint-initdb.d
          ports:
            - containerPort: 3306
          env:
            - name: MYSQL_ROOT_HOST
              value: "%"
            - name: MYSQL_ROOT_PASSWORD
              value: root
            - name: MYSQL_ARGS
              value: "--bind-address=0.0.0.0 --skip-name-resolve"
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
      # 声明存储卷
      volumes:
        - name: mysql-storage
          persistentVolumeClaim:
            claimName: xls-mysql-claim
        - name: mysql-init-script
          configMap:
            name: mysql-init-script
