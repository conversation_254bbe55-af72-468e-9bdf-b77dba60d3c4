apiVersion: apps/v1
kind: Deployment
metadata:
  name: xls-redis
spec:
  # pod的副本数
  replicas: 1
  selector:
    matchLabels:
      app: xls-redis
  # 描述pod是怎么样的
  template:
    metadata:
      # pod的标签要和selector的标签一致
      labels:
        app: xls-redis
    # pod的具体规范
    spec:
      containers:
        - name: xls-redis
          image: redis:latest
          # 优先使用本地,没有再从仓库拉取
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 6379
          resources:
            limits:
              cpu: 256m
              memory: 256Mi
