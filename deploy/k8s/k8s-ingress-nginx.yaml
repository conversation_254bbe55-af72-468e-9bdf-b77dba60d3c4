apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: xls-ingress
  labels:
    name: xls-ingress
spec:
  ingressClassName: nginx
  rules:
    # 通过什么域名访问
    - host: localhost
      http:
        paths:
          # 当访问前缀为 "/" 时，转发到xls服务
          - pathType: Prefix
            path: "/"
            backend:
              # 访问的service
              service:
                name: xls
                # service的端口
                port:
                  number: 80
