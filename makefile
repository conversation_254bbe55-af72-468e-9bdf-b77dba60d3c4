.PHONY: docker initk delk

# @ 阻止 Make 打印这些命令本身
# || true 是为了忽略错误继续执行
docker:
	GOOS=linux GOARCH=amd64 go build -o xls .
	docker rmi -f xls:latest || true
	docker build -t xls:latest -f deploy/docker/dockerfile .

initk:
	kubectl apply -f deploy/k8s/k8s-mysql-pvc.yaml
	kubectl apply -f deploy/k8s/k8s-mysql-pv.yaml

	kubectl apply -f deploy/k8s/k8s-mysql-deployment.yaml
	kubectl apply -f deploy/k8s/k8s-mysql-service.yaml

	kubectl apply -f deploy/k8s/k8s-redis-deployment.yaml
	kubectl apply -f deploy/k8s/k8s-redis-service.yaml

	kubectl apply -f deploy/k8s/k8s-ingress-nginx.yaml

	kubectl apply -f deploy/k8s/k8s-xls-deployment.yaml
	kubectl apply -f deploy/k8s/k8s-xls-service.yaml

delk:
	kubectl delete -f deploy/k8s/k8s-mysql-deployment.yaml || true
	kubectl delete -f deploy/k8s/k8s-mysql-service.yaml || true

	kubectl delete -f deploy/k8s/k8s-mysql-pvc.yaml --grace-period=0 --force || true
	kubectl delete -f deploy/k8s/k8s-mysql-pv.yaml --grace-period=0 --force || true

	kubectl delete -f deploy/k8s/k8s-redis-deployment.yaml || true
	kubectl delete -f deploy/k8s/k8s-redis-service.yaml || true

	kubectl delete -f deploy/k8s/k8s-ingress-nginx.yaml || true

	kubectl delete -f deploy/k8s/k8s-xls-deployment.yaml || true
	kubectl delete -f deploy/k8s/k8s-xls-service.yaml || true
