package main

import (
	"log/slog"
	"os"
	"strings"
	"time"
	"xls/config"
	"xls/internal/handler"
	"xls/internal/handler/auth"
	"xls/internal/middleware"
	"xls/internal/middleware/checklogin"
	"xls/internal/repository"
	"xls/internal/repository/cache"
	"xls/internal/repository/dao"
	"xls/internal/service"
	"xls/internal/service/sms/memory"
	withratelimit "xls/internal/service/sms/with_ratelimit"
	"xls/pkg/ratelimit"
	tokentool "xls/pkg/token_tool"

	"github.com/gin-gonic/contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/lmittmann/tint"
	slogGorm "github.com/orandin/slog-gorm"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	initLogger()

	config.InitConfig()

	server := initWebServer()

	server.GET("/ping", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{
			"message": "pong",
		})
	})

	server.Run(":8080")
}

func initLogger() {
	slog.SetDefault(slog.New(
		tint.NewHandler(os.Stderr, &tint.Options{
			AddSource: true,
			Level:     slog.LevelDebug,
		}),
	))
}

func initDB() *gorm.DB {
	db, err := gorm.Open(mysql.Open(config.NowConfig.MySqlUrl), &gorm.Config{
		Logger: slogGorm.New(
			slogGorm.WithSlowThreshold(10 * time.Millisecond),
			// slogGorm.WithTraceAll(),
		),
	})
	if err != nil {
		panic(err)
	}
	err = initSQLTable(db)
	if err != nil {
		panic(err)
	}
	return db
}

func initSQLTable(db *gorm.DB) error {
	// 一个小坑: gorm会自动给表名加上复数形式
	return db.AutoMigrate(&dao.User{}, &dao.Article{}, &dao.PublishArticle{})
}

func initRedis() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr: config.NowConfig.RedisUrl,
	})
}

func initWebServer() *gin.Engine {
	db := initDB()
	redis := initRedis()

	// gin.SetMode(gin.ReleaseMode)
	server := gin.Default()

	token := tokentool.NewJWTToken(redis)

	var authStrategy checklogin.AuthStrategy
	switch config.NowConfig.AuthStrategy {
	case "session":
		authStrategy = checklogin.NewSessionAuth(server)
	case "jwt":
		authStrategy = checklogin.NewTokenAuth(token)
	default:
		panic("unknown auth strategy")
	}

	useMiddleware(server, redis, authStrategy)

	registerRoutes(server, db, redis, token)

	return server
}

func useMiddleware(server *gin.Engine, redis redis.Cmdable, authStrategy checklogin.AuthStrategy) {
	// 初始化限流中间件
	server.Use(middleware.RedisRateLimiter(redis))
	// 初始化登录验证中间件
	server.Use(checklogin.CheckLogin(authStrategy))
	// 设置跨域CORS
	server.Use(cors.New(cors.Config{
		// 允许前端传入的header
		AllowedHeaders: []string{"Content-Type", "Authorization"},
		// 允许前端获取的header
		ExposedHeaders: []string{"Access-Token", "Refresh-Token"},
		// 允许跨域请求中携带cookie
		AllowCredentials: true,
		AllowOriginFunc: func(origin string) bool {
			if strings.Contains(origin, "localhost") {
				// 开发环境, 允许localhost通过
				return true
			}
			// 生产环境, 只允许指定域名通过
			// return strings.Contains(origin, "some-domain.com")
			return false
		},
		MaxAge: 12 * time.Hour,
	}))

	server.Use(middleware.HttpLogger(middleware.HttpLoggerOption{
		ShowReqBody:  true,
		ShowRespBody: true,
	}))
}

func registerRoutes(server *gin.Engine, db *gorm.DB, redis redis.Cmdable, token tokentool.Token) {
	userDAO := dao.NewUserGORM(db)
	userCache := cache.NewUserRedis(redis)
	userRepo := repository.NewUserRepoImpl(userDAO, userCache)
	userService := service.NewUserService(userRepo)

	vCodeCache := cache.NewVCodeRedis(redis)
	vCodeRepo := repository.NewVCodeRepo(vCodeCache)
	limiter := ratelimit.NewRedisSlideWindowLimiter(redis, 10*time.Second, 2)
	smsServiceWithLimiter := withratelimit.NewService(memory.NewService(), limiter)
	vCodeService := service.NewSmsVCodeService(vCodeRepo, smsServiceWithLimiter)

	var authHandler auth.AuthHandler
	switch config.NowConfig.AuthStrategy {
	case "session":
		authHandler = auth.NewSessionAuthHandler()
	case "jwt":
		temp := auth.NewTokenAuthHandler(token)
		temp.RegisterRoutes(server)
		authHandler = temp
	default:
		panic("unknown auth strategy")
	}

	userHandler := handler.NewUserHandler(userService, vCodeService, authHandler)
	userHandler.RegisterRoutes(server)

	articleDAO := dao.NewArticleGORM(db)
	authorRepo := repository.NewArticleRepo(articleDAO)
	articleService := service.NewArticleService(authorRepo)
	articleHandler := handler.NewArticleHandler(articleService, authHandler)
	articleHandler.RegisterRoutes(server)
}
