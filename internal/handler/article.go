package handler

import (
	"log/slog"
	"net/http"
	"xls/internal/handler/auth"
	"xls/internal/model"
	"xls/internal/service"

	"github.com/gin-gonic/gin"
)

type ArticleHandler struct {
	svc     service.ArticleService
	authHdl auth.AuthHandler
}

func NewArticleHandler(svc service.ArticleService, authHdl auth.AuthHandler) *ArticleHandler {
	return &ArticleHandler{
		svc:     svc,
		authHdl: authHdl,
	}
}

func (me *ArticleHandler) RegisterRoutes(server *gin.Engine) {
	g := server.Group("/article")
	g.POST("edit", me.Edit)
	g.POST("publish", me.Publish)
	g.POST("withdraw", me.Withdraw)

}

// 新建或编辑文章
func (me *ArticleHandler) Edit(ctx *gin.Context) {
	type Req struct {
		Id      int64  `json:"id"`
		Title   string `json:"title"`
		Content string `json:"content"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid request")
		return
	}

	authUser, ok := me.authHdl.GetAuthUser(ctx)
	if !ok {
		ctx.String(http.StatusUnauthorized, "authUser not found")
		return
	}

	id, err := me.svc.CreateOrUpdate(ctx, model.Article{
		Id:      req.Id,
		Title:   req.Title,
		Content: req.Content,
		Author: model.Author{
			Id: authUser.UserId,
		},
	})
	if err != nil {
		ctx.String(http.StatusInternalServerError, "系统错误")
		slog.Error("Edit: 保存文章失败", "err", err)
		return
	}

	ctx.JSON(200, gin.H{
		"id": id,
	})
}

// 新建或编辑, 并发布文章
func (me *ArticleHandler) Publish(ctx *gin.Context) {
	type Req struct {
		Id      int64  `json:"id"`
		Title   string `json:"title"`
		Content string `json:"content"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid request")
		return
	}

	authUser, ok := me.authHdl.GetAuthUser(ctx)
	if !ok {
		ctx.String(http.StatusUnauthorized, "authUser not found")
		return
	}

	id, err := me.svc.Publish(ctx, model.Article{
		Id:      req.Id,
		Title:   req.Title,
		Content: req.Content,
		Author: model.Author{
			Id: authUser.UserId,
		},
	})
	if err != nil {
		ctx.String(http.StatusInternalServerError, "系统错误")
		slog.Error("Publish: 发布文章失败", "err", err)
		return
	}

	ctx.JSON(200, gin.H{
		"id": id,
	})
}

func (me *ArticleHandler) Withdraw(ctx *gin.Context) {
	type Req struct {
		Id int64 `json:"id"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid request")
		return
	}

	authUser, ok := me.authHdl.GetAuthUser(ctx)
	if !ok {
		ctx.String(http.StatusUnauthorized, "authUser not found")
		return
	}

	err := me.svc.Withdraw(ctx, model.Article{
		Id: req.Id,
		Author: model.Author{
			Id: authUser.UserId,
		},
	})
	if err != nil {
		ctx.String(http.StatusInternalServerError, "系统错误")
		slog.Error("Withdraw: 撤回文章失败", "err", err)
		return
	}

	ctx.String(http.StatusOK, "Withdraw success")
}
