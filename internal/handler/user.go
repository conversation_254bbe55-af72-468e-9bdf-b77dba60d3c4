package handler

import (
	"net/http"
	"xls/internal/handler/auth"
	"xls/internal/model"
	"xls/internal/repository/cache"
	"xls/internal/repository/dao"
	"xls/internal/service"

	"log/slog"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	svc     service.UserService
	codeSvc service.VCodeService
	authHdl auth.AuthHandler
}

func NewUserHandler(svc service.UserService, codeSvc service.VCodeService, authHdl auth.AuthHandler) *UserHandler {
	return &UserHandler{
		svc:     svc,
		codeSvc: codeSvc,
		authHdl: authHdl,
	}
}

func (me *UserHandler) RegisterRoutes(server *gin.Engine) {
	g := server.Group("/user")
	g.POST("signup", me.SignUp)
	g.POST("login", me.Login)
	g.POST("logout", me.Logout)
	g.POST("edit", me.Edit)
	g.GET("profile", me.Profile)
	g.POST("login/sms", me.LoginSendSmsCode)
	g.POST("login/sms/verify", me.LoginVerifySmsCode)

}

func (me *UserHandler) SignUp(ctx *gin.Context) {
	type Req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid sign up request\n")
		return
	}

	if len(req.Email) == 0 || len(req.Password) == 0 {
		ctx.String(http.StatusBadRequest, "Email and Password are required\n")
		return
	}

	err := me.svc.SignUp(ctx, model.User{
		Email:    req.Email,
		Password: req.Password,
	})
	if err == dao.ErrUserDuplicate {
		ctx.String(http.StatusBadRequest, "User with this email already exists\n")
		return
	}
	if err != nil {
		slog.Error("SignUp: 往数据库保存注册的user失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.String(http.StatusOK, "Email: %s, Password: %s\n", req.Email, req.Password)
}

func (me *UserHandler) Login(ctx *gin.Context) {
	type Req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid login request\n")
		return
	}

	user, err := me.svc.Login(ctx, model.User{
		Email:    req.Email,
		Password: req.Password,
	})
	if err == service.ErrInvaildUserOrPassword {
		ctx.String(http.StatusBadRequest, "Invalid user or password\n")
		return
	}
	if err != nil {
		slog.Error("Login: 登录失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	err = me.authHdl.Login(ctx, user)
	if err != nil {
		slog.Error("authHdl Login: auth部分登录失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.String(http.StatusOK, "Login success. Email: %s\n", req.Email)
}

func (me *UserHandler) Logout(ctx *gin.Context) {
	err := me.authHdl.Logout(ctx)
	if err != nil {
		slog.Error("authHdl Logout: auth部分登出失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.String(http.StatusOK, "Logout success\n")
}

func (me *UserHandler) Edit(ctx *gin.Context) {
	type Req struct {
		Name  string `json:"name"`
		Intro string `json:"intro"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid edit request\n")
		return
	}

	authUser, ok := me.authHdl.GetAuthUser(ctx)
	if !ok {
		ctx.String(http.StatusUnauthorized, "authUser not found\n")
		return
	}

	uid := authUser.UserId
	user := model.User{
		Id:    uid,
		Name:  req.Name,
		Intro: req.Intro,
	}
	err := me.svc.Edit(ctx, user)
	if err != nil {
		slog.Error("Edit: 编辑user信息失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.String(http.StatusOK, "Edit success. Name: %s, Intro: %s\n", req.Name, req.Intro)
}

func (me *UserHandler) Profile(ctx *gin.Context) {
	authUser, ok := me.authHdl.GetAuthUser(ctx)
	if !ok {
		ctx.String(http.StatusUnauthorized, "authUser not found\n")
		return
	}

	uid := authUser.UserId
	user, err := me.svc.FindByID(ctx, uid)
	if err != nil {
		slog.Error("Profile: 根据ID查找user信息失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"id":    user.Id,
		"email": user.Email,
		"phone": user.Phone,
		"name":  user.Name,
		"intro": user.Intro,
	})
}

func (me *UserHandler) LoginSendSmsCode(ctx *gin.Context) {
	type Req struct {
		Phone string `json:"phone"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid login request\n")
		return
	}

	err := me.codeSvc.Send(ctx, "login", req.Phone)
	switch err {
	case nil:
	case cache.ErrCodeSendTooMany:
		slog.Warn("LoginSendSmsCode: 验证码发送次数过多", "err", err)
		ctx.String(http.StatusBadRequest, "Too many sms codes sent\n")
		return
	default:
		slog.Error("LoginSendSmsCode: 发送验证码失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}

	ctx.String(http.StatusOK, "Sms code sent\n")
}

func (me *UserHandler) LoginVerifySmsCode(ctx *gin.Context) {
	type Req struct {
		Phone string `json:"phone"`
		Code  string `json:"code"`
	}
	var req Req
	if err := ctx.Bind(&req); err != nil {
		ctx.String(http.StatusBadRequest, "Invalid login request\n")
		return
	}

	ok, err := me.codeSvc.Verify(ctx, "login", req.Code, req.Phone)
	if err != nil {
		slog.Error("LoginVerifySmsCode: 验证验证码失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}
	if !ok {
		ctx.String(http.StatusBadRequest, "Invalid sms code\n")
		return
	}

	user, err := me.svc.FindOrCreateByPhone(ctx, req.Phone)
	if err != nil {
		slog.Error("LoginVerifySmsCode: 根据手机号查找或创建user失败", "err", err)
		ctx.String(http.StatusInternalServerError, "系统错误")
		return
	}
	me.authHdl.Login(ctx, user)

	ctx.String(http.StatusOK, "Login success\n")
}
