package auth

import (
	"net/http"
	"strings"
	"xls/internal/model"
	tokentool "xls/pkg/token_tool"

	"log/slog"

	"github.com/gin-gonic/gin"
)

type TokenAuthHandler struct {
	token tokentool.Token
}

var _ AuthHandler = (*TokenAuthHandler)(nil)

func NewTokenAuthHandler(token tokentool.Token) *TokenAuthHandler {
	return &TokenAuthHandler{
		token: token,
	}
}

func (me *TokenAuthHandler) RegisterRoutes(server *gin.Engine) {
	g := server.Group("/auth")
	g.POST("refresh_token", me.RefreshToken)
}

func (me *TokenAuthHandler) RefreshToken(ctx *gin.Context) {
	newTokenStr, err := me.token.RefreshToken(ctx, me.GetTokenStr(ctx))
	switch err {
	case nil:
	case tokentool.ErrIncorrectRefreshToken:
		slog.Info("token种类不对", "err", err)
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	case tokentool.ErrTokenExpired:
		slog.Info("token过期", "err", err)
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	default:
		slog.Error("RefreshToken failed", "err", err)
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	ctx.Header("Access-Token", newTokenStr)
	ctx.String(http.StatusOK, "Refresh token success\n")
}

func (me *TokenAuthHandler) GetTokenStr(ctx *gin.Context) string {
	tokenStr := ctx.GetHeader("Authorization")
	return strings.TrimPrefix(tokenStr, "Bearer ")
}

func (me *TokenAuthHandler) Login(ctx *gin.Context, user model.User) error {
	authUser := user.ToAuthUser(ctx)
	accessTokenStr, err := me.token.GenerateAccessToken(authUser)
	if err != nil {
		return err
	}

	refreshTokenStr, err := me.token.GenerateRefreshToken(authUser)
	if err != nil {
		return err
	}

	ctx.Header("Access-Token", accessTokenStr)
	ctx.Header("Refresh-Token", refreshTokenStr)
	return nil
}

func (me *TokenAuthHandler) Logout(ctx *gin.Context) error {
	ctx.Header("Access-Token", "")
	ctx.Header("Refresh-Token", "")

	tokenStr := me.GetTokenStr(ctx)
	err := me.token.InvalidToken(ctx, tokenStr)
	return err
}

func (me *TokenAuthHandler) GetAuthUser(ctx *gin.Context) (model.AuthUser, bool) {
	temp, _ := ctx.Get(tokentool.AuthUserKey)
	authUser, ok := temp.(model.AuthUser)
	return authUser, ok
}
