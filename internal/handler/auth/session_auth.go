package auth

import (
	"xls/internal/model"
	tokentool "xls/pkg/token_tool"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

type SessionAuthHandler struct{}

var _ AuthHandler = (*SessionAuthHandler)(nil)

func NewSessionAuthHandler() *SessionAuthHandler {
	return &SessionAuthHandler{}
}

func (me *SessionAuthHandler) Login(ctx *gin.Context, user model.User) error {
	// 设置session的登录相关
	session := sessions.Default(ctx)
	session.Options(sessions.Options{
		// 生产环境建议开启
		// Secure:  true,
		// HttpOnly: true,
		MaxAge: 30 * 60,
	})

	// 设置user相关信息到session里
	authUser := user.ToAuthUser(ctx)
	session.Set(tokentool.AuthUserKey, authUser)

	session.Save()
	return nil
}

func (me *SessionAuthHandler) Logout(ctx *gin.Context) error {
	session := sessions.Default(ctx)
	session.Options(sessions.Options{
		// 生产环境建议开启
		// Secure:  true,
		// HttpOnly: true,
		MaxAge: -1,
	})
	session.Save()
	return nil
}

func (me *SessionAuthHandler) GetAuthUser(ctx *gin.Context) (model.AuthUser, bool) {
	session := sessions.Default(ctx)
	temp := session.Get(tokentool.AuthUserKey)
	authUser, ok := temp.(model.AuthUser)
	return authUser, ok
}
