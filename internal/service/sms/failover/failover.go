package failover

import (
	"context"
	"errors"
	"sync/atomic"
	"xls/internal/service/sms"

	"log/slog"
)

type Service struct {
	svcs []sms.Service
	idx  atomic.Uint64
}

var _ sms.Service = (*Service)(nil)

func NewService(svcs ...sms.Service) *Service {
	return &Service{
		svcs: svcs,
		idx:  atomic.Uint64{},
	}
}

func (me *Service) Send(ctx context.Context, templateId string, args []string, phoneNums ...string) error {
	idx := me.idx.Add(1)
	for i := range len(me.svcs) {
		idx := (idx + uint64(i)) % uint64(len(me.svcs))
		err := me.svcs[idx].Send(ctx, templateId, args, phoneNums...)
		switch err {
		case nil:
			return nil
		case context.DeadlineExceeded, context.Canceled:
			return err
		default:
			slog.Warn("sms failed, try next one", "err", err)
			continue
		}
	}
	return errors.New("all sms services failed")
}
