package tencent

import (
	"context"
	"fmt"
	"xls/internal/service/sms"

	tsms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20210111"
)

type Service struct {
	appId    *string
	signName *string
	client   *tsms.Client
}

var _ sms.Service = (*Service)(nil)

func NewService(appId, signName string, client *tsms.Client) *Service {
	return &Service{
		appId:    &appId,
		signName: &signName,
		client:   client,
	}
}

func (me *Service) Send(ctx context.Context, templateId string, args []string, phoneNums ...string) error {
	req := tsms.NewSendSmsRequest()
	req.SmsSdkAppId = me.appId
	req.SignName = me.signName
	req.TemplateId = &templateId
	req.PhoneNumberSet = stringPtrSlice(phoneNums)
	req.TemplateParamSet = stringPtrSlice(args)

	resp, err := me.client.SendSms(req)
	if err != nil {
		return err
	}
	for _, status := range resp.Response.SendStatusSet {
		if status.Code != nil && *status.Code != "Ok" {
			return fmt.Errorf("send sms failed: %s, %s\n", *status.Code, *status.Message)
		}
	}
	return nil
}

func stringPtrSlice(strs []string) []*string {
	s := make([]*string, len(strs))
	for i, str := range strs {
		s[i] = &str
	}
	return s
}
