package withratelimit

import (
	"context"
	"errors"
	"xls/internal/service/sms"
	"xls/pkg/ratelimit"

	"log/slog"
)

type Service struct {
	svc     sms.Service
	limiter ratelimit.Limiter
}

var _ sms.Service = (*Service)(nil)

func NewService(svc sms.Service, limiter ratelimit.Limiter) *Service {
	return &Service{
		svc:     svc,
		limiter: limiter,
	}
}

func (me *Service) Send(ctx context.Context, templateId string, args []string, phoneNums ...string) error {
	limited, err := me.limiter.Limit(ctx, "sms")
	if err != nil {
		return err
	}
	if limited {
		slog.Warn("too many requests", "last phoneNums", phoneNums)
		return errors.New("sms too many requests")
	}
	return me.svc.Send(ctx, templateId, args, phoneNums...)
}
