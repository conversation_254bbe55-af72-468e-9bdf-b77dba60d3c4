package service

import (
	"context"
	"fmt"
	"math/rand"
	"xls/internal/repository"
	"xls/internal/service/sms"
)

var templateId = "1"

type VCodeService interface {
	Send(ctx context.Context, useCase string, phone string) error
	Verify(ctx context.Context, useCase string, code string, phone string) (bool, error)
}

type smsVCodeService struct {
	repo repository.VCodeRepo
	svc  sms.Service
}

var _ VCodeService = (*smsVCodeService)(nil)

func NewSmsVCodeService(repo repository.VCodeRepo, sms sms.Service) *smsVCodeService {
	return &smsVCodeService{
		repo: repo,
		svc:  sms,
	}
}

func (me *smsVCodeService) Send(ctx context.Context, useCase string, phone string) error {
	code := me.generateCode()
	err := me.repo.Set(ctx, useCase, phone, code)
	if err != nil {
		return err
	}

	return me.svc.Send(ctx, templateId, []string{code}, phone)
}

func (me *smsVCodeService) Verify(ctx context.Context, useCase string, code string, phone string) (bool, error) {
	return me.repo.Verify(ctx, useCase, phone, code)
}

func (me *smsVCodeService) generateCode() string {
	// 生成6位随机数
	num := rand.Intn(1000000)
	return fmt.Sprintf("%06d", num)
}
