package service

import (
	"context"
	"xls/internal/model"
	"xls/internal/repository"
)

type ArticleService interface {
	CreateOrUpdate(ctx context.Context, article model.Article) (int64, error)
	Publish(ctx context.Context, article model.Article) (int64, error)
}

type articleService struct {
	repo repository.ArticleRepo
}

var _ ArticleService = (*articleService)(nil)

func NewArticleService(repo repository.ArticleRepo) ArticleService {
	return &articleService{
		repo: repo,
	}
}

func (me *articleService) CreateOrUpdate(ctx context.Context, article model.Article) (int64, error) {
	// 无论是新建还是编辑, 都是先回到未发布状态
	article.Status = model.ArticleStatusUnpublished
	if article.Id == 0 {
		return me.repo.Create(ctx, article)
	}
	return article.Id, me.repo.Update(ctx, article)
}

func (me *articleService) Publish(ctx context.Context, article model.Article) (int64, error) {
	article.Status = model.ArticleStatusPublished
	return me.repo.Sync(ctx, article)
}

func (me *articleService) Withdraw(ctx context.Context, article model.Article) error {
	article.Status = model.ArticleStatusHidden
	_, err := me.repo.Sync(ctx, article)
	return err
}
