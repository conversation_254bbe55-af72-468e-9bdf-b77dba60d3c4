package service

import (
	"context"
	"errors"
	"xls/internal/model"
	"xls/internal/repository"
	"xls/internal/repository/dao"
)

var (
	ErrInvaildUserOrPassword = errors.New("invalid user or password")
)

type UserService interface {
	SignUp(ctx context.Context, user model.User) error
	Login(ctx context.Context, user model.User) (model.User, error)
	FindByID(ctx context.Context, id int64) (model.User, error)
	Edit(ctx context.Context, user model.User) error
	FindOrCreateByPhone(ctx context.Context, phone string) (model.User, error)
}

type userService struct {
	repo repository.UserRepo
}

func NewUserService(repo repository.UserRepo) *userService {
	return &userService{repo: repo}
}

func (me *userService) SignUp(ctx context.Context, user model.User) error {
	err := user.EncryptPassword()
	if err != nil {
		return err
	}
	return me.repo.Create(ctx, user)
}

func (me *userService) Login(ctx context.Context, user model.User) (model.User, error) {
	userInRepo, err := me.repo.FindByEmail(ctx, user.Email)
	if err == dao.ErrUserNotFound {
		return model.User{}, ErrInvaildUserOrPassword
	}
	if err != nil {
		return model.User{}, err
	}

	// 比较密码
	err = user.ComparePassword(userInRepo.Password)
	if err != nil {
		return model.User{}, ErrInvaildUserOrPassword
	}

	return userInRepo, nil
}

func (me *userService) FindByID(ctx context.Context, id int64) (model.User, error) {
	return me.repo.FindByID(ctx, id)
}

func (me *userService) Edit(ctx context.Context, user model.User) error {
	return me.repo.Update(ctx, user)
}

func (me *userService) FindOrCreateByPhone(ctx context.Context, phone string) (model.User, error) {
	user, err := me.repo.FindByPhone(ctx, phone)
	switch err {
	case nil:
		return user, nil
	default:
		return user, err
	case dao.ErrUserNotFound:
		user := model.User{
			Phone: phone,
		}
		err = me.repo.Create(ctx, user)
		// 并发注册也算成功
		if err != nil && err != dao.ErrUserDuplicate {
			return user, err
		}
		// 可能会有主从延迟问题
		return me.repo.FindByPhone(ctx, phone)
	}
}
