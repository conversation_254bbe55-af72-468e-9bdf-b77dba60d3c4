package model

import (
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

type User struct {
	Id       int64
	Email    string
	Phone    string
	Password string
	Name     string
	Intro    string
	// 业务意义上的创建时间, 用于展示
	CreatedAt time.Time
}

func (me *User) EncryptPassword() error {
	encyptedPassword, err := bcrypt.GenerateFromPassword([]byte(me.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	me.Password = string(encyptedPassword)
	return nil
}

func (me *User) ComparePassword(hashedpassword string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedpassword), []byte(me.Password))
}

func (me *User) ToAuthUser(ctx *gin.Context) AuthUser {
	return AuthUser{
		UserId:    me.Id,
		UserAgent: ctx.Request.UserAgent(),
	}
}
