package model

import "time"

type Article struct {
	Id      int64
	Title   string
	Content string
	Author  Author
	Status  ArticleStatus

	CreatedAt time.Time
}

// Author 其实就是 User, 为了贴合文章中作者的概念, 单独定义一个结构体
type Author struct {
	Id   int64
	Name string
}

type ArticleStatus uint8

const (
	ArticleStatusUnknown ArticleStatus = iota
	ArticleStatusUnpublished
	ArticleStatusPublished
	ArticleStatusHidden
)
