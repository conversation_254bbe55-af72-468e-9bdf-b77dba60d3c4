package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"xls/internal/model"

	"github.com/redis/go-redis/v9"
)

var ErrNotExist = redis.Nil

type UserCache interface {
	Get(ctx context.Context, id int64) (model.User, error)
	Set(ctx context.Context, user model.User) error
	Del(ctx context.Context, id int64) error
}

type UserRedis struct {
	client      redis.Cmdable
	expireation time.Duration
}

var _ UserCache = (*UserRedis)(nil)

func NewUserRedis(client redis.Cmdable) *UserRedis {
	return &UserRedis{
		client:      client,
		expireation: 15 * time.Minute,
	}
}

func (me *UserRedis) key(id int64) string {
	return fmt.Sprintf("user:info:%d", id)
}

func (me *UserRedis) Get(ctx context.Context, id int64) (model.User, error) {
	val, err := me.client.Get(ctx, me.key(id)).Bytes()
	if err != nil {
		return model.User{}, err
	}
	var user model.User
	err = json.Unmarshal(val, &user)
	return user, err
}

func (me *UserRedis) Set(ctx context.Context, user model.User) error {
	json, err := json.Marshal(user)
	if err != nil {
		return err
	}
	return me.client.Set(ctx, me.key(user.Id), json, me.expireation).Err()
}

func (me *UserRedis) Del(ctx context.Context, id int64) error {
	return me.client.Del(ctx, me.key(id)).Err()
}
