-- 获取函数传进来key, lua中索引值从1开始
local key = KEYS[1]
-- 获取函数传进来数据
local input_code = ARGV[1]
local code = redis.call("get", key)

local count_key = key .. ":count"
local count = tonumber(redis.call("get", count_key))


if not count or count > 3 then
    -- 发送太频繁
    return -1
end

if not code then
    return -2
end

-- 验证码错误, 或者验证码过期被删了
if code ~= input_code then
    redis.call("incr", count_key)
    return -2
else
    redis.call("del", key)
    redis.call("del", count_key)
    return 0
end
