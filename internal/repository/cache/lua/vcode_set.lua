-- 获取函数传进来key, lua中索引值从1开始
local key = KEYS[1]
-- 获取函数传进来数据
local code = ARGV[1]
-- 过期时间
local ttl = redis.call("ttl", key)

-- 验证次数 ..拼接字符串
local count_key = key .. ":count"

-- -1 说明key存在, 但没有过期时间
if ttl == -1 then
    -- 约定 -2 表示系统错误
    return -2
end

-- -2 说明key不存在, 或者到了可以重新发送的时间
if ttl == -2 or ttl < 10 * 60 - 60 then
    redis.call("set", key, code)
    redis.call("expire", key, 10 * 60)
    redis.call("set", count_key, 1)
    redis.call("expire", count_key, 10 * 60)
    return 0
else
    -- 发送太频繁
    return -1
end
