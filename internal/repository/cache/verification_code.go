package cache

import (
	"context"
	_ "embed"
	"errors"
	"fmt"

	"github.com/redis/go-redis/v9"
)

//go:embed lua/vcode_set.lua
var setLua string

//go:embed lua/vcode_verify.lua
var verifyLua string

var ErrCodeSendTooMany = errors.New("验证码发送次数过多")
var ErrCodeVerifyTooMany = errors.New("验证码验证次数过多")

type VCodeCache interface {
	Set(ctx context.Context, useCase, phone, code string) error
	Verify(ctx context.Context, useCase, phone, code string) (bool, error)
}

type VCodeRedis struct {
	client redis.Cmdable
}

var _ VCodeCache = (*VCodeRedis)(nil)

func NewVCodeRedis(client redis.Cmdable) *VCodeRedis {
	return &VCodeRedis{
		client: client,
	}
}

func (me *VCodeRedis) Set(ctx context.Context, useCase, phone, code string) error {
	res, err := me.client.Eval(ctx, setLua, []string{me.key(useCase, phone)}, code).Int()
	if err != nil {
		return err
	}

	switch res {
	case 0:
		return nil
	case -1:
		return ErrCodeSendTooMany
	case -2:
		return errors.New("验证码未设置ttl")
	default:
		return errors.New("lua未知错误")
	}
}

func (me *VCodeRedis) key(useCase string, phone string) string {
	return fmt.Sprintf("vcode:%s:%s", useCase, phone)
}

func (me *VCodeRedis) Verify(ctx context.Context, useCase, phone, code string) (bool, error) {
	res, err := me.client.Eval(ctx, verifyLua, []string{me.key(useCase, phone)}, code).Int()
	if err != nil {
		return false, err
	}

	switch res {
	case 0:
		return true, nil
	case -1:
		return false, ErrCodeVerifyTooMany
	case -2:
		return false, nil
	default:
		return false, errors.New("lua未知错误")
	}
}
