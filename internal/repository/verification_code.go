package repository

import (
	"context"
	"xls/internal/repository/cache"
)

type VCodeRepo interface {
	Set(ctx context.Context, useCase, phone, code string) error
	Verify(ctx context.Context, useCase, phone, code string) (bool, error)
}

type vCodeRepo struct {
	cache cache.VCodeCache
}

var _ VCodeRepo = (*vCodeRepo)(nil)

func NewVCodeRepo(cache cache.VCodeCache) *vCodeRepo {
	return &vCodeRepo{
		cache: cache,
	}
}

func (me *vCodeRepo) Set(ctx context.Context, useCase, phone, code string) error {
	return me.cache.Set(ctx, useCase, phone, code)
}

func (me *vCodeRepo) Verify(ctx context.Context, useCase, phone, code string) (bool, error) {
	return me.cache.Verify(ctx, useCase, phone, code)
}
