package dao

import (
	"context"
	"database/sql"
	"errors"
	"time"
	"xls/internal/model"

	"github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
)

var (
	ErrUserDuplicate = errors.New("user with this email already exists")
	ErrUserNotFound  = gorm.ErrRecordNotFound
)

type UserDAO interface {
	Create(ctx context.Context, user User) error
	FindByEmail(ctx context.Context, email string) (User, error)
	FindByID(ctx context.Context, id int64) (User, error)
	FindByPhone(ctx context.Context, phone string) (User, error)
	Update(ctx context.Context, user User) error
}

type UserGORM struct {
	db *gorm.DB
}

var _ UserDAO = (*UserGORM)(nil)

func NewUserGORM(db *gorm.DB) *UserGORM {
	return &UserGORM{db: db}
}

// 直接对应数据库表的字段
type User struct {
	Id       int64          `gorm:"primary_key;autoIncrement"`
	Email    sql.NullString `gorm:"unique"`
	Phone    sql.NullString `gorm:"unique"`
	Password string
	Name     string
	Intro    string
	// 统一UTC+0的毫秒时间戳
	CreatedAt int64 `gorm:"autoCreateTime:milli"`
	UpdatedAt int64 `gorm:"autoUpdateTime:milli"`
}

func (me *User) ToModel() model.User {
	return model.User{
		Id:        me.Id,
		Email:     me.Email.String,
		Phone:     me.Phone.String,
		Password:  me.Password,
		Name:      me.Name,
		Intro:     me.Intro,
		CreatedAt: time.UnixMilli(me.CreatedAt),
	}
}

func UserModelToDAO(user model.User) User {
	return User{
		Id:       user.Id,
		Email:    sql.NullString{String: user.Email, Valid: user.Email != ""},
		Phone:    sql.NullString{String: user.Phone, Valid: user.Phone != ""},
		Password: user.Password,
		Name:     user.Name,
		Intro:    user.Intro,
	}
}

func (me *UserGORM) Create(ctx context.Context, user User) error {
	err := me.db.WithContext(ctx).Create(&user).Error
	if mysqlErr, ok := err.(*mysql.MySQLError); ok {
		// 1062: 唯一索引冲突
		if mysqlErr.Number == 1062 {
			return ErrUserDuplicate
		}
	}
	return err
}

func (me *UserGORM) FindByEmail(ctx context.Context, email string) (User, error) {
	var u User
	err := me.db.WithContext(ctx).Where("email = ?", email).First(&u).Error
	return u, err
}

func (me *UserGORM) FindByID(ctx context.Context, id int64) (User, error) {
	var u User
	err := me.db.WithContext(ctx).First(&u, id).Error
	return u, err
}

func (me *UserGORM) FindByPhone(ctx context.Context, phone string) (User, error) {
	var u User
	err := me.db.WithContext(ctx).Where("phone = ?", phone).First(&u).Error
	return u, err
}

func (me *UserGORM) Update(ctx context.Context, user User) error {
	// 存在的字段才会更新, 字段为零值则不更新
	return me.db.WithContext(ctx).Model(&user).Updates(user).Error
}
