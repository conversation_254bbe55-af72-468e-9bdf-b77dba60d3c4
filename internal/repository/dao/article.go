package dao

import (
	"context"
	"fmt"
	"xls/internal/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ArticleDAO interface {
	Create(ctx context.Context, article Article) (int64, error)
	Update(ctx context.Context, article Article) error
	Sync(ctx context.Context, article Article) (int64, error)
	InsertOrUpdate(ctx context.Context, article PublishArticle) error
}

type Article struct {
	Id       int64  `gorm:"primary_key;autoIncrement"`
	Title    string `gorm:"type:varchar(255)"`
	Content  string `gorm:"type:text"`
	AuthorId int64  `gorm:"index:author_id_created_at,sort:desc"`
	Status   uint8

	CreatedAt int64 `gorm:"autoCreateTime:milli;index:author_id_created_at,sort:desc"`
	UpdatedAt int64 `gorm:"autoUpdateTime:milli"`
}

type PublishArticle struct {
	Article
}

func ArticleModelToDAO(article model.Article) Article {
	return Article{
		Id:       article.Id,
		Title:    article.Title,
		Content:  article.Content,
		AuthorId: article.Author.Id,
		Status:   uint8(article.Status),
	}
}

type ArticleGORM struct {
	db *gorm.DB
}

var _ ArticleDAO = (*ArticleGORM)(nil)

func NewArticleGORM(db *gorm.DB) *ArticleGORM {
	return &ArticleGORM{db: db}
}

func (me *ArticleGORM) Transaction(ctx context.Context, fn func(gorm *ArticleGORM) error) error {
	return me.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		gorm := &ArticleGORM{db: tx}
		return fn(gorm)
	})
}

func (me *ArticleGORM) Create(ctx context.Context, article Article) (int64, error) {
	err := me.db.WithContext(ctx).Create(&article).Error
	return article.Id, err
}

func (me *ArticleGORM) Update(ctx context.Context, article Article) error {
	res := me.db.WithContext(ctx).Model(&article).
		Where("id = ? AND author_id = ?", article.Id, article.AuthorId).
		Updates(article)
	if res.Error != nil {
		return res.Error
	}
	// 防止更新其他用户的文章
	if res.RowsAffected == 0 {
		return fmt.Errorf("找不到id与作者id匹配的文章, id: %d, author_id: %d", article.Id, article.AuthorId)
	}
	return nil
}

// Sync 会先更新草稿表, 然后更新发布表, 两者存放在不同的表上
func (me *ArticleGORM) Sync(ctx context.Context, article Article) (int64, error) {
	return me.Transaction(ctx, func(gorm *ArticleGORM) error {
		
	var (
		id  = article.Id
		err error
	)
	// 先处理草稿表
	if id == 0 {
		id, err = me.Create(ctx, article)
	} else {
		err = me.Update(ctx, article)
	}
	if err != nil {
		return 0, err
	}
	// 再处理发布表
	return id, me.InsertOrUpdate(ctx, PublishArticle{Article: article})
}
}

func (me *ArticleGORM) InsertOrUpdate(ctx context.Context, article PublishArticle) error {
	return me.db.WithContext(ctx).Clauses(clause.OnConflict{
		UpdateAll: true,
	}).Create(&article).Error
}
