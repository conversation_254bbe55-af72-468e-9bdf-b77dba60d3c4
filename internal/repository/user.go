package repository

import (
	"context"
	"xls/internal/model"
	"xls/internal/repository/cache"
	"xls/internal/repository/dao"

	"log/slog"
)

type UserRepo interface {
	Create(ctx context.Context, user model.User) error
	FindByID(ctx context.Context, id int64) (model.User, error)
	FindByEmail(ctx context.Context, email string) (model.User, error)
	FindByPhone(ctx context.Context, phone string) (model.User, error)
	Update(ctx context.Context, user model.User) error
}

type UserRepoImpl struct {
	dao   dao.UserDAO
	cache cache.UserCache
}

var _ UserRepo = (*UserRepoImpl)(nil)

func NewUserRepoImpl(dao dao.UserDAO, cache cache.UserCache) *UserRepoImpl {
	return &UserRepoImpl{
		dao:   dao,
		cache: cache,
	}
}

func (me *UserRepoImpl) Create(ctx context.Context, user model.User) error {
	return me.dao.Create(ctx, dao.UserModelToDAO(user))
}

func (me *UserRepoImpl) FindByID(ctx context.Context, id int64) (model.User, error) {
	userInCache, err := me.cache.Get(ctx, id)
	if err == nil {
		return userInCache, nil
	}

	userInDAO, err := me.dao.FindByID(ctx, id)
	if err != nil {
		return model.User{}, err
	}

	user := userInDAO.ToModel()
	err = me.cache.Set(ctx, user)
	if err != nil {
		slog.Error("FindByID: 往redis保存user失败", "err", err)
	}

	return user, nil
}

func (me *UserRepoImpl) FindByEmail(ctx context.Context, email string) (model.User, error) {
	userInDAO, err := me.dao.FindByEmail(ctx, email)
	if err != nil {
		return model.User{}, err
	}

	return userInDAO.ToModel(), nil
}

func (me *UserRepoImpl) FindByPhone(ctx context.Context, phone string) (model.User, error) {
	userInDAO, err := me.dao.FindByPhone(ctx, phone)
	if err != nil {
		return model.User{}, err
	}

	return userInDAO.ToModel(), nil
}

func (me *UserRepoImpl) Update(ctx context.Context, user model.User) error {
	err := me.dao.Update(ctx, dao.User{
		Id:    user.Id,
		Name:  user.Name,
		Intro: user.Intro,
	})

	errCache := me.cache.Del(ctx, user.Id)
	if errCache != nil {
		slog.Error("Update: 删除缓存失败", "err", errCache)
	}
	return err
}
