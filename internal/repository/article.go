package repository

import (
	"context"
	"xls/internal/model"
	"xls/internal/repository/dao"
)

type ArticleRepo interface {
	Create(ctx context.Context, article model.Article) (int64, error)
	Update(ctx context.Context, article model.Article) error
	Sync(ctx context.Context, article model.Article) (int64, error)
}

type articleRepo struct {
	dao dao.ArticleDAO
}

var _ ArticleRepo = (*articleRepo)(nil)

func NewArticleRepo(dao dao.ArticleDAO) *articleRepo {
	return &articleRepo{
		dao: dao,
	}
}

func (me *articleRepo) Create(ctx context.Context, article model.Article) (int64, error) {
	return me.dao.Create(ctx, dao.ArticleModelToDAO(article))
}

func (me *articleRepo) Update(ctx context.Context, article model.Article) error {
	return me.dao.Update(ctx, dao.ArticleModelToDAO(article))
}

func (me *articleRepo) Sync(ctx context.Context, article model.Article) (int64, error) {
	return me.dao.Sync(ctx, dao.ArticleModelToDAO(article))
}

