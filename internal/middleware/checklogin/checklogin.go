package checklogin

import (
	"slices"

	"github.com/gin-gonic/gin"
)

var ignorePaths = []string{
	"/user/login",
	"/user/signup",
	"/user/login/sms",
	"/user/login/sms/verify",
	"/auth/refresh_token",
}

func CheckLogin(authStrategy AuthStrategy) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if slices.Contains(ignorePaths, ctx.Request.URL.Path) {
			return
		}
		authStrategy.CheckLogin(ctx)
	}
}

type AuthStrategy interface {
	CheckLogin(ctx *gin.Context)
}
