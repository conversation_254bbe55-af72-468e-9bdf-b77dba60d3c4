package checklogin

import (
	"encoding/gob"
	"net/http"
	"time"
	"xls/config"
	"xls/internal/model"
	tokentool "xls/pkg/token_tool"

	"log/slog"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
)

type SessionAuth struct{}

var _ AuthStrategy = (*SessionAuth)(nil)

func NewSessionAuth(server *gin.Engine) *SessionAuth {
	// 设置session
	store, err := redis.NewStore(16, "tcp",
		config.NowConfig.RedisUrl, "",
		config.NowConfig.Secret)
	if err != nil {
		panic(err)
	}
	server.Use(sessions.Sessions("xls_session", store))

	// 设置Redis序列化方式, 不然存不进去
	gob.Register(model.AuthUser{})

	return &SessionAuth{}
}

func (me *SessionAuth) CheckLogin(ctx *gin.Context) {
	authUser, ok := me.GetAuthUser(ctx)
	if !ok {
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	if authUser.UserAgent != ctx.Request.UserAgent() {
		slog.Warn("UA in authUser is different from current UA",
			"authUser UA", authUser.UserAgent, "current UA", ctx.Request.UserAgent())
		// 生产环境应该返回401, 这里为了方便测试不处理
		// ctx.AbortWithStatus(http.StatusUnauthorized)
		// return
	}

	// 刷新session
	session := sessions.Default(ctx)
	now := time.Now().Unix()
	// 为什么不获取cookie的maxAge呢？ 因为cookie容易被篡改
	updateTime := session.Get("update_time")
	// 第一次登录||距离上次更新时间超过一定时间||authUser有变化
	if updateTime == nil || now-updateTime.(int64) > 10*60 {
		session.Options(sessions.Options{MaxAge: 30 * 60})
		session.Set("update_time", now)
		session.Save()
	}
}

func (me *SessionAuth) GetAuthUser(ctx *gin.Context) (model.AuthUser, bool) {
	session := sessions.Default(ctx)
	temp := session.Get(tokentool.AuthUserKey)
	authUser, ok := temp.(model.AuthUser)
	return authUser, ok
}
