package checklogin

import (
	"net/http"
	"strings"
	"xls/internal/model"
	tokentool "xls/pkg/token_tool"

	"log/slog"

	"github.com/gin-gonic/gin"
)

type TokenAuth struct {
	token tokentool.Token
}

var _ AuthStrategy = (*TokenAuth)(nil)

func NewTokenAuth(token tokentool.Token) *TokenAuth {
	return &TokenAuth{token: token}
}

func (me *TokenAuth) CheckLogin(ctx *gin.Context) {
	tokenStr := me.GetTokenStr(ctx)
	claims, err := me.token.ParseToken(tokenStr)
	if err != nil {
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	if claims.GetStdSubject() != "access_token" {
		slog.Warn("CheckLogin: token is not access_token", "tokenStr", tokenStr)
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	authUser := claims.GetData().(model.AuthUser)
	if authUser.UserAgent != ctx.Request.UserAgent() {
		slog.Warn("UA in authUser is different from current UA",
			"authUser UA", authUser.UserAgent, "current UA", ctx.Request.UserAgent())
		// 生产环境应该返回401, 这里为了方便测试不处理
		// ctx.AbortWithStatus(http.StatusUnauthorized)
		// return
	}

	count, err := me.token.CountToken(ctx, tokenStr)
	if err != nil {
		slog.Error("CheckLogin: 查看token是否过期时错误", "err", err)
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
	if count > 0 {
		slog.Info("CheckLogin: token is expired", "tokenStr", tokenStr)
		ctx.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	ctx.Set(tokentool.AuthUserKey, claims.GetData())
}

func (me *TokenAuth) GetTokenStr(ctx *gin.Context) string {
	tokenStr := ctx.GetHeader("Authorization")
	return strings.TrimPrefix(tokenStr, "Bearer ")
}
