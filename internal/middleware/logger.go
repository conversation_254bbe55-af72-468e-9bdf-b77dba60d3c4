package middleware

import (
	"bytes"
	"io"
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"
)

type HttpLoggerOption struct {
	ShowReqBody  bool
	ShowRespBody bool
}

type HttpLog struct {
	Method     string
	Url        string
	StatusCode int
	ReqBody    string
	RespBody   string
	Duration   string
}

func HttpLogger(option HttpLoggerOption) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		start := time.Now()
		httpLog := &HttpLog{
			Method: ctx.Request.Method,
			Url:    ctx.Request.URL.String(),
		}

		if option.ShowReqBody && ctx.Request.Body != nil {
			body, _ := ctx.GetRawData()
			// 需要把body放回去
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
			httpLog.ReqBody = string(body)
		}

		if option.ShowRespBody {
			writer := &respWriter{ctx.Writer, httpLog}
			ctx.Writer = writer
		}

		defer func() {
			httpLog.Duration = time.Since(start).String()
			slog.Debug("http请求", "httpLog", httpLog)
		}()

		ctx.Next()
	}
}

type respWriter struct {
	gin.ResponseWriter
	log *HttpLog
}

func (me *respWriter) Write(b []byte) (n int, err error) {
	me.log.RespBody = string(b)
	return me.ResponseWriter.Write(b)
}

func (me *respWriter) WriteHeader(code int) {
	me.log.StatusCode = code
	me.ResponseWriter.WriteHeader(code)
}

func (me *respWriter) WriteString(s string) (n int, err error) {
	me.log.RespBody = s
	return me.ResponseWriter.WriteString(s)
}
