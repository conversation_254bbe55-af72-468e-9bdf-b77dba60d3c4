package middleware

import (
	"fmt"
	"net/http"
	"time"
	"xls/pkg/ratelimit"

	"log/slog"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

type RateLimiter struct {
	limiter   ratelimit.Limiter
	keyPrefix string
}

func NewRateLimiter(limiter ratelimit.Limiter, keyPrefix string) *RateLimiter {
	return &RateLimiter{
		limiter:   limiter,
		keyPrefix: keyPrefix,
	}
}

func (me *RateLimiter) key(ctx *gin.Context) string {
	return fmt.Sprintf("%s:%s", me.keyPrefix, ctx.ClientIP())
}

func RedisRateLimiter(client redis.Cmdable) gin.HandlerFunc {
	redisLimiter := ratelimit.NewRedisSlideWindowLimiter(client, 1*time.Second, 100)
	r := NewRateLimiter(redisLimiter, "ip_limit")
	return func(ctx *gin.Context) {
		key := r.key(ctx)
		limited, err := r.limiter.Limit(ctx, key)
		if err != nil {
			slog.Error("redisLimiter Limit error", "err", err)
			ctx.AbortWithStatus(http.StatusInternalServerError)
			return
		}
		if limited {
			ctx.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"error": "too many requests",
			})
			return
		}

		ctx.Next()
	}
}
